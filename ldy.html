<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta http-equiv="Expires" content="-1" />
    <meta http-equiv="pragram" content="no-cache" />
    <meta name="referrer" content="unsafe-url">
    <meta name="apple-touch-fullscreen" content="yes" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <meta name="screen-orientation" content="portrait">
    <meta name="x5-orientation" content="portrait">
    <meta name="full-screen" content="yes">
    <meta name="x5-fullscreen" content="true">
    <title></title>

    <!-- 内联关键样式，确保页面基本结构不依赖外部CSS -->
    <style>
        * {
            margin: 0;
            padding: 0;
        }

        body {
            background-color: #fff;
        }

        .page {
            display: flex;
            flex-direction: column;
            background-color: #fff;
        }

        .bg {
            width: 100%;
        }

        .hidden {
            display: none !important;
        }

        .btn-box {
            width: 100%;
            position: absolute;
            top: 139vw;
            left: 0;
            text-align: center;
        }

        .btn {
            width: 80vw;
            border: none;
            color: #fff;
        }

        .an_scale {
            animation-name: Scale;
            animation-iteration-count: infinite;
            animation-duration: 1500ms;
            animation-fill-mode: none;
            animation-timing-function: linear;
        }

        @keyframes Scale {
            0% {
                transform: scale(1);
            }

            50% {
                transform: scale(1.1);
            }

            100% {
                transform: scale(1);
            }
        }

        /* 基础样式，确保页面在CSS加载前也能正常显示 */
        a {
            text-decoration: none;
            color: #666;
            font-size: 12px;
        }

        .appinfo {
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            transform: scale(0.8);
            text-align: center;
            flex-wrap: wrap;
        }

        .appinfo span {
            display: inline-block;
            padding: 0 4px;
            font-size: 14px;
            color: #666;
        }

        .ts {
            position: absolute;
            top: 17%;
            width: 46.5px;
            right: 0;
            z-index: 10;
        }

        .box-img {
            width: 100%;
        }

        #page-back {
            width: 15vw;
            height: 8vw;
            line-height: 8vw;
            color: #fff;
            font-size: 4vw;
            text-align: center;
            background-color: #029CFE;
            border-top-right-radius: 4vw;
            border-bottom-right-radius: 4vw;
            position: absolute;
            left: 0;
            top: 2vw;
            display: none;
        }
    </style>

    <script>
        // ========== 内联关键调起逻辑 ==========

        // 全局变量
        var pkg, report_key, page, channel, userEntry, uctrackid, configId;
        var pageHide = false;
        var dataKey = "my-router-btn";
        var QkBtnImg = "./img/button.png";
        var JsonData = {};
        var ua = navigator.userAgent.toLowerCase();
        var factorystr;

        // 获取URL参数函数
        function getQueryVariable(variable) {
            var query = window.location.search.substring(1);
            var vars = query.split("&");
            for (var i = 0; i < vars.length; i++) {
                var pair = vars[i].split("=");
                if (pair[0] == variable) {
                    return decodeURIComponent(pair[1]);
                }
            }
            return false;
        }

        // 初始化URL参数
        function initParams() {
            pkg = getQueryVariable('pkg');
            report_key = getQueryVariable('report_key');
            page = getQueryVariable('page');
            channel = getQueryVariable('channel');
            userEntry = getQueryVariable('userEntry');
            uctrackid = getQueryVariable('uctrackid');
            configId = getQueryVariable('configId');
            factorystr = factory(ua);
        }

        // 设备厂商判断函数
        function isoppo(ua) {
            if (/(oppo|heytap)/g.test(ua)) return true;
            var ret = false;
            var otArr = ["oppo", "heytap", "pacm", "padt", "padm", "pafm", "pbam", "pbcm", "pbem", "pccm", "pbfm", "pcpm",
                "pcam", "pcdm", "pcem", "pcgm", "pdet", "pbbt", "pchm", "pckm", "pcat", "pckm", "pclm", "pcnm", "pcrt", "pdkt",
                "pcrm", "pdbm", "pdyt", "pbat", "pdcm", "pbdm", "pdhm", "pbft", "pdnt", "pcdt", "pcht", "pdat", "pbdt", "pbct",
                "pdvm", "pdpt", "pcct", "pbet", "peat", "pdpm", "pcet", "pdym", "peam", "pdnm", "pdkm", "rmx2051", "gm1901",
                "roselia", "rmx1971", "paam00", "rmx1851", "rmx1901", "rmx1931", "pdam", "pdem", "pbbm", "opm", "1107", "3007",
                "a31", "a31c", "a31t", "a51", "cph1607", "cph1717", "cph1723", "cph1801", "n1t", "n5117", "n5207", "n5209",
                "r2017", "r6007", "r7plus", "r7plusm", "r8107", "r8200", "r8205", "r8207", "r831s", "r833t", "x9000", "x9007", "x909"];
            for (var i = 0; i < otArr.length; i++) {
                if (ua.indexOf(otArr[i]) > -1) {
                    ret = true;
                    break;
                }
            }
            return ret;
        }

        function xiaomi(ua) {
            return 1 < (ua.match(/.*(xiaomi|redmi|mix|mi\s).*/i) || []).length;
        }

        function isvivo(ua) {
            return /(vivo|; v1|; v2)/g.test(ua);
        }

        function factory(ua) {
            if (ua.indexOf("huawei") > -1 || ua.indexOf('honor') > -1) return 'huawei';
            if (xiaomi(ua)) return 'xiaomi';
            if (isvivo(ua)) return 'vivo';
            if (isoppo(ua)) return 'oppo';
            return 'other';
        }

        // 构建参数字符串
        function httpBuildParams() {
            return 'userEntry=' + userEntry + '&channel=' + channel + '&uctrackid=' + uctrackid + "&appid=" + report_key + "&configId=" + configId;
        }

        // 点击跳转函数
        function clickHap() {
            window.location.href = 'hap://app/' + pkg + '?' + httpBuildParams();
        }

        // 唤醒落地页
        function jumpPage() {
            try {
                window.location.href = 'hap://app/' + pkg + '?' + httpBuildParams();
            } catch (e) { }
        }

        // 核心调起函数
        function launchFun() {
            try {
                var bodyclickArea = document.getElementById('bodyclickArea');
                if (bodyclickArea) {
                    bodyclickArea.addEventListener('click', clickHap);
                }
            } catch (e) { }

            jumpPage();

            try {
                if (typeof isEnvSupportRouter !== 'undefined') {
                    isEnvSupportRouter(function (bAvailable) {
                        if (bAvailable) {
                            setTimeout(() => {
                                if (!pageHide && typeof routeToQuickapp !== 'undefined') {
                                    routeToQuickapp(dataKey);
                                }
                            }, 300);
                        }
                    });
                }
            } catch (e) { }
        }

        // 数据上报函数
        function dataReport(event) {
            console.log('上报数据', event);
            if (!event) return;

            var reportUrl = "https://stateless.unity.cn/release/1156179e-6f72-4b30-940a-8f7fca6efaa5/ad_report?appid_key=" + report_key + "&report_name=" + event;

            fetch(reportUrl)
                .then(response => {
                    if (response.ok) {
                        console.log("数据上报成功: " + event);
                    } else {
                        console.error("数据上报失败: " + event + ", 状态码: " + response.status);
                    }
                })
                .catch(error => {
                    console.error("数据上报出错: " + event, error);
                });
        }

        // ========== 优化的加载流程 ==========

        // 第一步：立即加载外部脚本
        function loadExternalScript() {
            var script = document.createElement('script');
            script.src = 'https://jits5.heytapdownload.com/cms-jits-heytapmobi-com/iframe/qa_router.min.js';
            script.onload = function () {
                console.log('外部脚本加载完成');
                // 第二步：执行调起逻辑
                executelaunchLogic();
            };
            script.onerror = function () {
                console.log('外部脚本加载失败，继续执行调起逻辑');
                // 即使外部脚本加载失败，也要执行调起逻辑
                executelaunchLogic();
            };
            document.head.appendChild(script);
        }

        // 第二步：执行调起逻辑
        function executelaunchLogic() {
            console.log('开始执行调起逻辑');
            initParams();
            dataReport('start');

            // 如果uctrackid不存在或者为空则上报
            if (!uctrackid) {
                dataReport('noUtrackid');
            }

            // 立即执行调起逻辑
            launchFun();

            // 第三步：延迟加载页面内容
            setTimeout(function () {
                loadPageContent();
            }, 100);
        }

        // 第三步：动态加载页面内容
        function loadPageContent() {
            console.log('开始加载页面内容');

            // 异步加载完整的main.js功能
            loadMainScript();

            // 异步加载CSS
            loadCSS();

            // 渲染页面内容
            renderPageContent();
        }

        // 异步加载CSS
        function loadCSS() {
            var link = document.createElement('link');
            link.rel = 'stylesheet';
            link.type = 'text/css';
            link.href = './css/style.css';
            document.head.appendChild(link);
        }

        // 异步加载完整的main.js功能
        function loadMainScript() {
            // 加载其他依赖的JS文件
            var scripts = ['./js/main.js'];

            scripts.forEach(function (src) {
                var script = document.createElement('script');
                script.src = src;
                script.async = true;
                document.head.appendChild(script);
            });
        }

        // 渲染页面内容
        function renderPageContent() {
            var pageContent = document.getElementById('page-content');

            // 创建页面结构
            pageContent.innerHTML = `
                <div class="page">
                    <div style="position: relative;">
                        <div id="page-back" onclick="pageBack()">返回</div>
                        <div class="appinfo isShowView">
                            <span id="appname">app名称</span>
                            <span id="version">app版本信息</span>
                        </div>
                        <div class="appinfo isShowView">
                            <span id="company">开发者信息</span>
                            <span id='linkTypeFlag'></span>
                        </div>
                        <div class="appinfo isShowView" style="text-align: center;">
                            <span id="des"></span>
                        </div>
                        <div class="appExp isShowView" style="text-align: center;">
                            <span><a onclick="licensing()">app的权限列表及用途说明</a></span>
                            <span><a onclick="privacy()">app的用户隐私协议</a></span>
                        </div>
                        <img src="" id="complain" class="ts" onclick="clickComplaint()">
                        <div class="box-img" style="position: relative;" id="bodyclickArea">
                            <img src="" id="bg" class="bg">
                            <!--H5点击组件区域-->
                            <div id="btn-area" style="position: absolute;top:125vw;width: 100vw;"></div>
                        </div>
                    </div>
                </div>
            `;

            // 显示页面内容
            pageContent.classList.remove('hidden');

            // 初始化页面功能
            initPageFeatures();
        }

        // 初始化页面功能
        function initPageFeatures() {
            // 生成快应用按钮
            function qkButton() {
                return `
                    <qa-router-button id="app-btn" data-key="${dataKey}" data-package-name="${pkg}" data-page="/"
                        data-params="{}" data-design-params='{"fontSize": 16,"designWidth": 1080}'
                        data-click-event='{"eventName": "handleClickEvent", "eventParams": "anyString"}'
                        data-expose-event='{"eventName": "handleExposeEvent", "eventParams": "anyString"}'
                        >
                        <templates>
                            <div class="btn-box">
                                <img src="${QkBtnImg}" id="btn" class="btn an_scale">
                            </div>
                        </templates>
                        <styles>
                            .btn-box{
                            width:100%;
                            }
                            img {
                            display:block;
                            width:80%;
                            margin:0 auto;
                            }
                            .an_scale {
                            animation-name: Scale;
                            animation-iteration-count: infinite;
                            animation-duration: 1500ms;
                            animation-fill-mode: none;
                            animation-timing-function: linear;
                            }

                            @keyframes Scale {
                            0% {
                            transform: scale(1);
                            }

                            50% {
                            transform: scale(1.1);
                            }

                            100% {
                            transform: scale(1);
                            }
                            }
                        </styles>
                    </qa-router-button>
                `;
            }

            // 设置按钮区域
            var btnArea = document.getElementById('btn-area');
            if (btnArea) {
                btnArea.innerHTML = qkButton();
            }

            // 异步加载配置数据
            loadConfigData();
        }

        // 异步加载配置数据
        function loadConfigData() {
            if (!configId) {
                console.log('configId不存在，使用默认配置');
                JsonData = { automatic: '1' };
                updatePageContent();
                return;
            }

            const API_ENDPOINT = "https://c.unity.cn";
            const APP_ID = "1156179e-6f72-4b30-940a-8f7fca6efaa5";
            const APP_SERVICE = "6c72cddc6a644625b7bc4e8980b48617";

            // 获取basic auth的Header
            function get_basic_authorization(app_id, app_secret) {
                const credentials = app_id + ':' + app_secret;
                const encodedCredentials = btoa(credentials);
                return { 'Authorization': 'Basic ' + encodedCredentials };
            }

            const options = {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    ...get_basic_authorization(APP_ID, APP_SERVICE),
                }
            };

            // 异步请求配置数据
            fetch(API_ENDPOINT + '/v1/configs/' + configId, options)
                .then(response => response.json())
                .then(data => {
                    try {
                        JsonData = JSON.parse(data.config.value);
                    } catch (e) {
                        console.error('解析配置数据失败:', e);
                        JsonData = { automatic: '1' };
                    }
                    updatePageContent();
                })
                .catch(error => {
                    console.error('加载配置数据失败:', error);
                    JsonData = { automatic: '1' };
                    updatePageContent();
                });
        }

        // 更新页面内容
        function updatePageContent() {
            var companyEle = document.getElementById('company');
            var versionEle = document.getElementById('version');
            var bgEle = document.getElementById('bg');
            var complainEle = document.getElementById('complain');
            var appnameEle = document.getElementById('appname');
            var isShowView = document.getElementsByClassName('isShowView');
            var des = document.getElementById('des');

            if (JsonData && (JsonData.background || (JsonData.backgrounds && JsonData.backgrounds.length))) {
                if (JsonData.factor_switch && JsonData.factor_switch != 1) {
                    for (var i = 0; i < isShowView.length; i++) {
                        isShowView[i].innerHTML = '';
                    }
                } else {
                    if (companyEle) {
                        companyEle.innerHTML = '开发者信息：' + JsonData.company;
                    }
                    if (versionEle) {
                        versionEle.innerHTML = 'app版本信息：' + JsonData.version;
                    }
                    if (appnameEle) {
                        appnameEle.innerHTML = '快应用app名称：' + JsonData.app;
                    }
                    if (des && JsonData.des) {
                        des.innerHTML = JsonData.des;
                    }
                }
                if (bgEle) {
                    bgEle.setAttribute('src', JsonData.background);
                }
                if (complainEle) {
                    complainEle.setAttribute('src', JsonData.complain);
                }
            } else {
                if (bgEle) {
                    bgEle.setAttribute('src', 'https://a.unity.cn/client_api/v1/buckets/6a839c9f-a1f5-475f-8ce6-5d63437f099c/content/default.jpg');
                }
                if (complainEle) {
                    complainEle.setAttribute('src', 'https://a.unity.cn/client_api/v1/buckets/6a839c9f-a1f5-475f-8ce6-5d63437f099c/content/feed.png');
                }
            }
        }

        // 页面功能函数
        function pageBack() {
            history.go(-1);
        }

        function licensing() {
            window.location.href = 'privacy/user_interface.html?service=%E6%B8%B8%E7%8E%A9%E7%9B%8A%E6%99%BA%E6%B8%B8%E6%88%8F&product_name=' + (JsonData.app || '');
        }

        function privacy() {
            window.location.href = 'privacy/privacy_product.html?service=%E6%B8%B8%E7%8E%A9%E7%9B%8A%E6%99%BA%E6%B8%B8%E6%88%8F&product_name=' + (JsonData.app || '');
        }

        function clickComplaint() {
            if (typeof webTrack !== 'undefined') {
                webTrack('clickComplaint');
            }
            window.location.replace('complaint.html');
        }

        function handleClickEvent() { }
        function handleExposeEvent() { }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function () {
            console.log('页面DOM加载完成，开始优化流程');
            // 第一步：立即加载外部脚本
            loadExternalScript();
        });
    </script>
</head>

<body>
    <!-- 初始状态：完全空白的页面 -->
    <div id="page-content" class="hidden">
        <!-- 页面内容将通过JavaScript动态加载 -->
    </div>
</body>

</html>