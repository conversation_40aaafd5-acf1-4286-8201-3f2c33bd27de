<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta http-equiv="Expires" content="-1" />
    <meta http-equiv="pragram" content="no-cache" />
    <meta name="referrer" content="unsafe-url">
    <meta name="apple-touch-fullscreen" content="yes" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <meta name="screen-orientation" content="portrait">
    <meta name="x5-orientation" content="portrait">
    <meta name="full-screen" content="yes">
    <meta name="x5-fullscreen" content="true">
    <title></title>

    <!-- 内联关键样式，确保页面基本结构不依赖外部CSS -->
    <style>
        * {
            margin: 0;
            padding: 0;
        }

        body {
            background-color: #fff;
        }

        .page {
            display: flex;
            flex-direction: column;
            background-color: #fff;
        }

        .bg {
            width: 100%;
        }

        .hidden {
            display: none !important;
        }

        .btn-box {
            width: 100%;
            position: absolute;
            top: 139vw;
            left: 0;
            text-align: center;
        }

        .btn {
            width: 80vw;
            border: none;
            color: #fff;
        }

        .an_scale {
            animation-name: Scale;
            animation-iteration-count: infinite;
            animation-duration: 1500ms;
            animation-fill-mode: none;
            animation-timing-function: linear;
        }

        @keyframes Scale {
            0% {
                transform: scale(1);
            }

            50% {
                transform: scale(1.1);
            }

            100% {
                transform: scale(1);
            }
        }

        /* 基础样式，确保页面在CSS加载前也能正常显示 */
        a {
            text-decoration: none;
            color: #666;
            font-size: 12px;
        }

        .appinfo {
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            transform: scale(0.8);
            text-align: center;
            flex-wrap: wrap;
        }

        .appinfo span {
            display: inline-block;
            padding: 0 4px;
            font-size: 14px;
            color: #666;
        }

        .ts {
            position: absolute;
            top: 17%;
            width: 46.5px;
            right: 0;
            z-index: 10;
        }

        .box-img {
            width: 100%;
        }

        #page-back {
            width: 15vw;
            height: 8vw;
            line-height: 8vw;
            color: #fff;
            font-size: 4vw;
            text-align: center;
            background-color: #029CFE;
            border-top-right-radius: 4vw;
            border-bottom-right-radius: 4vw;
            position: absolute;
            left: 0;
            top: 2vw;
            display: none;
        }
    </style>

    <script>
        // ========== 最精简的调起逻辑 ==========

        // 核心全局变量
        var pkg, report_key, page, channel, userEntry, uctrackid, configId;
        var pageHide = false;
        var dataKey = "my-router-btn";

        // 获取URL参数函数
        function getQueryVariable(variable) {
            var query = window.location.search.substring(1);
            var vars = query.split("&");
            for (var i = 0; i < vars.length; i++) {
                var pair = vars[i].split("=");
                if (pair[0] == variable) {
                    return decodeURIComponent(pair[1]);
                }
            }
            return false;
        }

        // 初始化核心参数
        function initCoreParams() {
            pkg = getQueryVariable('pkg');
            report_key = getQueryVariable('report_key');
            page = getQueryVariable('page');
            channel = getQueryVariable('channel');
            userEntry = getQueryVariable('userEntry');
            uctrackid = getQueryVariable('uctrackid');
            configId = getQueryVariable('configId');
        }

        // 构建参数字符串
        function httpBuildParams() {
            return 'userEntry=' + userEntry + '&channel=' + channel + '&uctrackid=' + uctrackid + "&appid=" + report_key + "&configId=" + configId;
        }

        // 点击跳转函数
        function clickHap() {
            window.location.href = 'hap://app/' + pkg + '?' + httpBuildParams();
        }

        // 唤醒落地页
        function jumpPage() {
            try {
                window.location.href = 'hap://app/' + pkg + '?' + httpBuildParams();
            } catch (e) { }
        }

        // 核心调起函数
        function launchFun() {
            jumpPage();

            try {
                if (typeof isEnvSupportRouter !== 'undefined') {
                    isEnvSupportRouter(function (bAvailable) {
                        if (bAvailable) {
                            setTimeout(() => {
                                if (!pageHide && typeof routeToQuickapp !== 'undefined') {
                                    routeToQuickapp(dataKey);
                                }
                            }, 300);
                        }
                    });
                }
            } catch (e) { }
        }

        // 简化的数据上报函数
        function dataReport(event) {
            if (!event || !report_key) return;

            var reportUrl = "https://stateless.unity.cn/release/1156179e-6f72-4b30-940a-8f7fca6efaa5/ad_report?appid_key=" + report_key + "&report_name=" + event;

            try {
                fetch(reportUrl).catch(function () {
                    // 静默处理错误，不影响调起逻辑
                });
            } catch (e) {
                // 兼容不支持fetch的环境
            }
        }

        // ========== 优化的加载流程 ==========

        // 第一步：立即加载外部脚本
        function loadExternalScript() {
            var script = document.createElement('script');
            script.src = 'https://jits5.heytapdownload.com/cms-jits-heytapmobi-com/iframe/qa_router.min.js';
            script.onload = function () {
                console.log('外部脚本加载完成');
                executelaunchLogic();
            };
            script.onerror = function () {
                console.log('外部脚本加载失败，继续执行调起逻辑');
                executelaunchLogic();
            };
            document.head.appendChild(script);
        }

        // 第二步：执行调起逻辑
        function executelaunchLogic() {
            console.log('开始执行调起逻辑');
            initCoreParams();
            dataReport('start');

            // 如果uctrackid不存在或者为空则上报
            if (!uctrackid) {
                dataReport('noUtrackid');
            }

            // 立即执行调起逻辑
            launchFun();

            // 调起后延迟加载页面内容和功能
            setTimeout(function () {
                loadPageFeatures();
            }, 100);
        }

        // 第三步：调起后加载页面功能
        function loadPageFeatures() {
            console.log('调起完成，开始加载页面功能');

            // 异步加载CSS
            var link = document.createElement('link');
            link.rel = 'stylesheet';
            link.type = 'text/css';
            link.href = './css/style.css';
            document.head.appendChild(link);

            // 异步加载main.js（包含所有页面功能）
            var script = document.createElement('script');
            script.src = './js/main.js';
            script.async = true;
            script.onload = function () {
                console.log('main.js加载完成，页面功能已就绪');
                // main.js加载完成后，会自动初始化页面内容
            };
            document.head.appendChild(script);
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function () {
            console.log('页面DOM加载完成，开始优化流程');
            launchFun();
            // 立即加载外部脚本并执行调起逻辑
            loadExternalScript();
        });
    </script>
</head>

<body>
    <!-- 初始状态：完全空白的页面 -->
    <div id="page-content" class="hidden">
        <!-- 页面内容将通过JavaScript动态加载 -->
    </div>
</body>

</html>