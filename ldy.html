<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta http-equiv="Expires" content="-1" />
    <meta http-equiv="pragram" content="no-cache" />
    <meta name="referrer" content="unsafe-url">
    <meta name="apple-touch-fullscreen" content="yes" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <meta name="screen-orientation" content="portrait">
    <meta name="x5-orientation" content="portrait">
    <meta name="full-screen" content="yes">
    <meta name="x5-fullscreen" content="true">
    <title></title>
    <script>
        function addMainJs() {
            var scriptMain = document.createElement('script');
            scriptMain.src = './js/main.js';
            document.head.appendChild(scriptMain);
        }

        var script = document.createElement('script');
        script.src = 'https://jits5.heytapdownload.com/cms-jits-heytapmobi-com/iframe/qa_router.min.js';
        script.onload = function () {
            addMainJs();
        };
        document.head.appendChild(script);
    </script>
    <link rel="stylesheet" type="text/css" href="./css/style.css">
</head>

<body>
    <div class="page">
        <div style="position: relative;">
            <div id="page-back" onclick="pageBack()">返回</div>
            <div class="appinfo isShowView">
                <span id="appname">app名称</span>
                <span id="version">app版本信息</span>
            </div>
            <div class="appinfo isShowView">
                <span id="company">开发者信息</span>
                <span id='linkTypeFlag'></span>
            </div>
            <div class="appinfo isShowView" style="text-align: center;">
                <span id="des"></span>
            </div>
            <div class="appExp isShowView" style="text-align: center;">
                <span><a onclick="licensing()">app的权限列表及用途说明</a></span>
                <span><a onclick="privacy()">app的用户隐私协议</a></span>
            </div>
            <img src="" id="complain" class="ts" onclick="clickComplaint()">
            <div class="box-img" style="position: relative;" id="bodyclickArea">
                <img src="" id="bg" class="bg">
                <!--H5点击组件区域-->
                <div id="btn-area" style="position: absolute;top:125vw;width: 100vw;"></div>
            </div>
        </div>
    </div>
</body>

</html>