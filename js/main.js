// ========== 页面功能模块 ==========
// 此文件在调起逻辑执行后异步加载，包含所有页面展示和交互功能

console.log('main.js 开始加载页面功能');

// 从全局变量获取参数（由ldy.html设置）
var pkg = window.pkg || getQueryVariable('pkg');
var report_key = window.report_key || getQueryVariable('report_key');
var page = window.page || getQueryVariable('page');
var channel = window.channel || getQueryVariable('channel');
var userEntry = window.userEntry || getQueryVariable('userEntry');
var uctrackid = window.uctrackid || getQueryVariable('uctrackid');
var configId = window.configId || getQueryVariable('configId');

var pageHide = window.pageHide || false;
var dataKey = window.dataKey || "my-router-btn";
var QkBtnImg = "./img/button.png";
var JsonData = {};
var ua = navigator.userAgent.toLowerCase();
var factorystr = factory(ua);

// 页面功能初始化
function initPageFeatures() {
    console.log('初始化页面功能');

    // 渲染页面内容
    renderPageContent();

    // 加载配置数据
    loadConfigData();

    // 设置页面监听器
    setupPageListeners();
}

// 渲染页面内容
function renderPageContent() {
    var pageContent = document.getElementById('page-content');
    if (!pageContent) return;

    // 创建页面结构
    pageContent.innerHTML = `
        <div class="page">
            <div style="position: relative;">
                <div id="page-back" onclick="pageBack()">返回</div>
                <div class="appinfo isShowView">
                    <span id="appname">app名称</span>
                    <span id="version">app版本信息</span>
                </div>
                <div class="appinfo isShowView">
                    <span id="company">开发者信息</span>
                    <span id='linkTypeFlag'></span>
                </div>
                <div class="appinfo isShowView" style="text-align: center;">
                    <span id="des"></span>
                </div>
                <div class="appExp isShowView" style="text-align: center;">
                    <span><a onclick="licensing()">app的权限列表及用途说明</a></span>
                    <span><a onclick="privacy()">app的用户隐私协议</a></span>
                </div>
                <img src="" id="complain" class="ts" onclick="clickComplaint()">
                <div class="box-img" style="position: relative;" id="bodyclickArea">
                    <img src="" id="bg" class="bg">
                    <!--H5点击组件区域-->
                    <div id="btn-area" style="position: absolute;top:125vw;width: 100vw;"></div>
                </div>
            </div>
        </div>
    `;

    // 显示页面内容
    pageContent.classList.remove('hidden');

    // 设置按钮区域
    var btnArea = document.getElementById('btn-area');
    if (btnArea) {
        btnArea.innerHTML = qkButton();
    }

    // 为bodyclickArea添加点击事件（如果还没有的话）
    try {
        var bodyclickArea = document.getElementById('bodyclickArea');
        if (bodyclickArea && typeof clickHap !== 'undefined') {
            bodyclickArea.addEventListener('click', clickHap);
        }
    } catch (e) { }
}

// 生成快应用按钮
function qkButton() {
    return `
        <qa-router-button id="app-btn" data-key="${dataKey}" data-package-name="${pkg}" data-page="/"
            data-params="{}" data-design-params='{"fontSize": 16,"designWidth": 1080}'
            data-click-event='{"eventName": "handleClickEvent", "eventParams": "anyString"}'
            data-expose-event='{"eventName": "handleExposeEvent", "eventParams": "anyString"}'
            >
            <templates>
                <div class="btn-box">
                    <img src="${QkBtnImg}" id="btn" class="btn an_scale">
                </div>
            </templates>
            <styles>
                .btn-box{
                width:100%;
                }
                img {
                display:block;
                width:80%;
                margin:0 auto;
                }
                .an_scale {
                animation-name: Scale;
                animation-iteration-count: infinite;
                animation-duration: 1500ms;
                animation-fill-mode: none;
                animation-timing-function: linear;
                }

                @keyframes Scale {
                0% {
                transform: scale(1);
                }

                50% {
                transform: scale(1.1);
                }

                100% {
                transform: scale(1);
                }
                }
            </styles>
        </qa-router-button>
    `;
}

// 异步加载配置数据
function loadConfigData() {
    if (!configId) {
        console.log('configId不存在，使用默认配置');
        JsonData = { automatic: '1' };
        updatePageContent();
        return;
    }

    const API_ENDPOINT = "https://c.unity.cn";
    const APP_ID = "1156179e-6f72-4b30-940a-8f7fca6efaa5";
    const APP_SERVICE = "6c72cddc6a644625b7bc4e8980b48617";

    const options = {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            ...get_basic_authorization(APP_ID, APP_SERVICE),
        }
    };

    // 异步请求配置数据
    fetch(API_ENDPOINT + '/v1/configs/' + configId, options)
        .then(response => response.json())
        .then(data => {
            try {
                JsonData = JSON.parse(data.config.value);
            } catch (e) {
                console.error('解析配置数据失败:', e);
                JsonData = { automatic: '1' };
            }
            updatePageContent();
        })
        .catch(error => {
            console.error('加载配置数据失败:', error);
            JsonData = { automatic: '1' };
            updatePageContent();
        });
}

// 更新页面内容
function updatePageContent() {
    var companyEle = document.getElementById('company');
    var versionEle = document.getElementById('version');
    var bgEle = document.getElementById('bg');
    var complainEle = document.getElementById('complain');
    var appnameEle = document.getElementById('appname');
    var isShowView = document.getElementsByClassName('isShowView');
    var des = document.getElementById('des');

    if (JsonData && (JsonData.background || (JsonData.backgrounds && JsonData.backgrounds.length))) {
        if (JsonData.factor_switch && JsonData.factor_switch != 1) {
            for (var i = 0; i < isShowView.length; i++) {
                isShowView[i].innerHTML = '';
            }
        } else {
            if (companyEle) {
                companyEle.innerHTML = '开发者信息：' + JsonData.company;
            }
            if (versionEle) {
                versionEle.innerHTML = 'app版本信息：' + JsonData.version;
            }
            if (appnameEle) {
                appnameEle.innerHTML = '快应用app名称：' + JsonData.app;
            }
            if (des && JsonData.des) {
                des.innerHTML = JsonData.des;
            }
        }
        if (bgEle) {
            bgEle.setAttribute('src', JsonData.background);
        }
        if (complainEle) {
            complainEle.setAttribute('src', JsonData.complain);
        }
    } else {
        if (bgEle) {
            bgEle.setAttribute('src', 'https://a.unity.cn/client_api/v1/buckets/6a839c9f-a1f5-475f-8ce6-5d63437f099c/content/default.jpg');
        }
        if (complainEle) {
            complainEle.setAttribute('src', 'https://a.unity.cn/client_api/v1/buckets/6a839c9f-a1f5-475f-8ce6-5d63437f099c/content/feed.png');
        }
    }
}

// 设置页面监听器
function setupPageListeners() {
    var ksBackThree = JsonData.back_three;
    var ksBackNoLimit = JsonData.back_no_limit;

    //监听页面隐藏和显示
    try {
        var visibilityChange_str, visibilityChange;
        if (typeof document.hidden !== "undefined") {
            visibilityChange_str = "hidden";
            visibilityChange = "visibilitychange";
        } else if (typeof document.mozHidden !== "undefined") {
            visibilityChange_str = "mozHidden";
            visibilityChange = "mozvisibilitychange";
        } else if (typeof document.msHidden !== "undefined") {
            visibilityChange_str = "msHidden";
            visibilityChange = "msvisibilitychange";
        } else if (typeof document.webkitHidden !== "undefined") {
            visibilityChange_str = "webkitHidden";
            visibilityChange = "webkitvisibilitychange";
        }

        if (visibilityChange) {
            document.addEventListener(
                visibilityChange,
                function () {
                    if (!document[visibilityChange_str]) {
                        var btnArea = document.getElementById('btn-area');
                        if (btnArea) {
                            btnArea.innerHTML = '';
                            btnArea.innerHTML = qkButton();
                        }
                        console.log('页面显示');
                        try {
                            if (ksBackThree) {
                                //拉三次
                                var launchTimesFlag = getCookie('launchTimesFlag') || 1;
                                var newlaunchTimesFlag = Number(launchTimesFlag) + 1;
                                if (Number(newlaunchTimesFlag) <= 3) {
                                    // 调用全局的launchFun函数
                                    if (typeof window.launchFun === 'function') {
                                        window.launchFun();
                                    }
                                    var currentDate = new Date();
                                    var midnight = new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate
                                        .getDate() + 1, 0, 0, 0);
                                    var expires = midnight.toUTCString();
                                    document.cookie = "launchTimesFlag=" + newlaunchTimesFlag + "; expires=" + expires +
                                        "; path=/";
                                }
                            } else {
                                if (ksBackNoLimit) {
                                    //无限拉起
                                    if (typeof window.launchFun === 'function') {
                                        window.launchFun();
                                    }
                                }
                            }
                        } catch (e) { }
                        if (pageHide) {
                            pageHide = false;
                        }
                    } else {
                        console.log('页面隐藏');
                        pageHide = true;
                    }
                }
            );
        }
    } catch (e) {
        console.log(e);
    }
}

// ========== 页面功能函数 ==========

// 页面功能函数
function pageBack() {
    history.go(-1);
}

function licensing() {
    window.location.href = 'privacy/user_interface.html?service=%E6%B8%B8%E7%8E%A9%E7%9B%8A%E6%99%BA%E6%B8%B8%E6%88%8F&product_name=' + (JsonData.app || '');
}

function privacy() {
    window.location.href = 'privacy/privacy_product.html?service=%E6%B8%B8%E7%8E%A9%E7%9B%8A%E6%99%BA%E6%B8%B8%E6%88%8F&product_name=' + (JsonData.app || '');
}

function clickComplaint() {
    if (typeof webTrack !== 'undefined') {
        webTrack('clickComplaint');
    }
    window.location.replace('complaint.html');
}

function handleClickEvent() { }
function handleExposeEvent() { }

//打开来源包Scheme
function openScheme(schemeUrl) {
    var link = document.createElement('a');
    link.href = schemeUrl;
    document.body.appendChild(link);
    link.click();
}

//创建iframe
function aif(e, id = '') {
    var a = document["createElement"]("iframe");
    a["id"] = id;
    a["style"]["display"] = "none";
    a["src"] = e;
    var b = document["getElementsByTagName"]("body")[0];
    if (b) {
        b["appendChild"](a)
    } else {
        window["onload"] = function () {
            document["body"]["appendChild"](a)
        }
    }
}


function getCookie(name) {
    var arr = document.cookie.match(new RegExp("(^| )" + name + "=([^;]*)(;|$)"));
    var lret = getlocalStoryage(name);
    if (arr != null) return unescape(arr[2]);
    if (!lret) return lret;
    return null;
}

function getQueryVariable(variable) {
    var query = window.location.search.substring(1);
    var vars = query.split("&");
    for (var i = 0; i < vars.length; i++) {
        var pair = vars[i].split("=");
        if (pair[0] == variable) {
            return decodeURIComponent(pair[1]);
        }
    }
    return (false);
}

// oppo 判断
function isoppo(ua) {
    if (/(oppo|heytap)/g.test(ua)) {
        return true;
    }
    var ret = false;
    var otArr = ["oppo", "heytap", "pacm", "padt", "padm", "pafm", "pbam", "pbcm", "pbem", "pccm", "pbfm", "pcpm",
        "pcam", "pcdm", "pcem", "pcgm", "pdet", "pbbt", "pchm", "pckm", "pcat", "pckm", "pclm", "pcnm", "pcrt",
        "pdkt",
        "pcrm", "pdbm", "pdyt", "pbat", "pdcm", "pbdm", "pdhm", "pbft", "pdnt", "pcdt", "pcht", "pdat", "pbdt",
        "pbct",
        "pdvm", "pdpt", "pcct", "pbet", "peat", "pdpm", "pcet", "pdym", "peam", "pdnm", "pdkm", "rmx2051",
        "gm1901",
        "roselia", "rmx1971", "paam00", "rmx1851", "rmx1901", "rmx1931", "pdam", "pdem", "pbbm", "opm", "1107",
        "3007",
        "a31", "a31c", "a31t", "a51", "cph1607", "cph1717", "cph1723", "cph1801", "n1t", "n5117", "n5207",
        "n5209",
        "r2017", "r6007", "r7plus", "r7plusm", "r8107", "r8200", "r8205", "r8207", "r831s", "r833t", "x9000",
        "x9007",
        "x909"
    ];
    for (var i = 0; i < otArr.length; i++) {
        if (ua.indexOf(otArr[i]) > -1) {
            ret = true;
            break;
        }
    }
    return ret;
}
//是否是小米手机
function xiaomi(ua) {
    return 1 < (ua.match(/.*(xiaomi|redmi|mix|mi\s).*/i) || []).length
}
//是否是vivo手机
function isvivo(ua) {
    return /(vivo|; v1|; v2)/g.test(ua);
}

function factory(ua) {
    if (ua.indexOf("huawei") > -1 || ua.indexOf('honor') > -1) {
        return 'huawei';
    }
    if (xiaomi(ua)) {
        return 'xiaomi';
    }
    if (isvivo(ua)) {
        return 'vivo';
    }
    if (isoppo(ua)) {
        return 'oppo';
    }
    return 'other';
}

function getQueryString(e) {
    if ((e = e || location.href).split("?").length <= 1) return {};
    var t = e.split("?")[1];
    if (!t) return {};
    for (var n = {},
        i = 0,
        a = (t = t.split("&")).length; i < a; i++) {
        var o = t[i].split("=");
        n["" + o[0]] = o[1]
    }
    return n
}

// ========== 工具函数 ==========

function httpBuildParams() {
    return 'userEntry=' + userEntry + '&channel=' + channel + '&uctrackid=' + uctrackid + "&appid=" + report_key + "&configId=" + configId;
}

function get_basic_authorization(app_id, app_secret) {
    /** 获取basic auth的Header */
    const credentials = app_id + ':' + app_secret;
    const encodedCredentials = btoa(credentials);
    return { 'Authorization': 'Basic ' + encodedCredentials };
}

// ========== 页面功能自动初始化 ==========

// 当main.js加载完成后自动初始化页面功能
console.log('main.js加载完成，开始初始化页面功能');
initPageFeatures();