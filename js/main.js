var pkg = getQueryVariable('pkg'),
    report_key = getQueryVariable('report_key'),
    page = getQueryVariable('page'),
    channel = getQueryVariable('channel'),
    userEntry = getQueryVariable('userEntry'),
    uctrackid = getQueryVariable('uctrackid'),
    configId = getQueryVariable('configId');
let pageHide = false;

launchFun();

var dataKey = "my-router-btn"

let QkBtnImg = "./img/button.png"

function qkButton() {
    return `
			<qa-router-button id="app-btn" data-key=${dataKey} data-package-name=${pkg} data-page=/
				data-params={} data-design-params='{"fontSize": 16,"designWidth": 1080}'
				data-click-event='{"eventName": "handleClickEvent", "eventParams": "anyString"}'
				data-expose-event='{"eventName": "handleExposeEvent", "eventParams": "anyString"}'
				>
				<templates>
					<div class="btn-box">
						<img src=${QkBtnImg}
							id="btn" class="btn an_scale">
					</div>
				</templates>
				<styles>
					.btn-box{
					width:100%;
					}
					img {
					display:block;
					width:80%;
					margin:0 auto;
					}
					.an_scale {
					animation-name: Scale;
					animation-iteration-count: infinite;
					animation-duration: 1500ms;
					animation-fill-mode: none;
					animation-timing-function: linear;
					}

					@keyframes Scale {
					0% {
					transform: scale(1);
					}

					50% {
					transform: scale(1.1);
					}

					100% {
					transform: scale(1);
					}
					}
				</styles>
			</qa-router-button>
		`
}

document.getElementById('btn-area').innerHTML = qkButton();

launchFun();
launchFun();

var ua = navigator.userAgent.toLowerCase(),
    factorystr = factory(ua);

const API_ENDPOINT = "https://c.unity.cn";

const APP_ID = "1156179e-6f72-4b30-940a-8f7fca6efaa5";
const APP_SERVICE = "6c72cddc6a644625b7bc4e8980b48617";

const options = {
    method: 'get',
    url: `${API_ENDPOINT}/v1/configs/${configId}`,
    headers: {
        'Content-Type': 'application/json',
        ...get_basic_authorization(APP_ID, APP_SERVICE),
    },
    responseType: 'json',
};

var JsonData = {}; // json数据
try {
    var ajax = new XMLHttpRequest();
    ajax.open(options.method, options.url, false);

    for (const header in options.headers) {
        ajax.setRequestHeader(header, options.headers[header]);
    }
    ajax.send();
    JsonData = JSON.parse(JSON.parse(ajax.responseText).config.value)
} catch (e) {
    console.error(e);
    JsonData = {
        automatic: '1'
    }
}

dataReport('start')

// 如果uctrackid不存在或者为空则上报
if (!uctrackid) {
    dataReport('noUtrackid')
}

var companyEle = document.getElementById('company'),
    versionEle = document.getElementById('version'),
    bgEle = document.getElementById('bg'),
    btnEle = document.getElementById('btn'),
    complainEle = document.getElementById('complain'),
    appnameEle = document.getElementById('appname'),
    isShowView = document.getElementsByClassName('isShowView'),
    des = document.getElementById('des');;

if (JsonData && (JsonData.background || (JsonData.backgrounds && JsonData.backgrounds.length))) {
    if (JsonData.factor_switch && JsonData.factor_switch != 1) {
        for (var i = 0; i < isShowView.length; i++) {
            isShowView[i].innerHTML = ''
        }
    } else {
        if (companyEle) {
            companyEle.innerHTML = '开发者信息：' + JsonData.company
        }
        if (versionEle) {
            versionEle.innerHTML = 'app版本信息：' + JsonData.version
        }
        if (appnameEle) {
            appnameEle.innerHTML = '快应用app名称：' + JsonData.app
        }
        if (des) {
            if (JsonData.des) {
                des.innerHTML = JsonData.des
            }
        }
    }
    if (bgEle) {
        bgEle.setAttribute('src', JsonData.background)
    }
    if (complainEle) {
        complainEle.setAttribute('src', JsonData.complain)
    }
} else {
    if (bgEle) {
        bgEle.setAttribute('src', 'https://a.unity.cn/client_api/v1/buckets/6a839c9f-a1f5-475f-8ce6-5d63437f099c/content/default.jpg')
    }
    if (complainEle) {
        complainEle.setAttribute('src', 'https://a.unity.cn/client_api/v1/buckets/6a839c9f-a1f5-475f-8ce6-5d63437f099c/content/feed.png')
    }
}



function clickHap() {
    window.location.href = 'hap://app/' + pkg + '?' + httpBuildParams();
}

//自动拉起方法
function launchFun() {
    try {
        document.getElementById('bodyclickArea').addEventListener('click', clickHap);
    } catch (e) { }
    jumpPage();
    try {
        isEnvSupportRouter(function (bAvailable) {
            if (bAvailable) {
                setTimeout(() => {
                    if (!pageHide) {
                        routeToQuickapp(dataKey);
                    }
                }, 300)
            }
        })
    } catch (e) { }
}

var ksBackThree = JsonData.back_three;
var ksBackNoLimit = JsonData.back_no_limit;

//监听页面隐藏和显示
try {
    if (typeof document.hidden !== "undefined") {
        visibilityChange_str = "hidden";
        visibilityChange = "visibilitychange";
    } else if (typeof document.mozHidden !== "undefined") {
        visibilityChange_str = "mozHidden";
        visibilityChange = "mozvisibilitychange";
    } else if (typeof document.msHidden !== "undefined") {
        visibilityChange_str = "msHidden";
        visibilityChange = "msvisibilitychange";
    } else if (typeof document.webkitHidden !== "undefined") {
        visibilityChange_str = "webkitHidden";
        visibilityChange = "webkitvisibilitychange";
    }
    document.addEventListener(
        visibilityChange,
        function () {
            if (!document[visibilityChange_str]) {
                document.getElementById('btn-area').innerHTML = '';
                document.getElementById('btn-area').innerHTML = qkButton();
                console.log('页面显示');
                try {
                    if (ksBackThree) {
                        //拉三次
                        var launchTimesFlag = getCookie('launchTimesFlag') || 1;
                        var newlaunchTimesFlag = Number(launchTimesFlag) + 1;
                        if (Number(newlaunchTimesFlag) <= 3) {
                            launchFun();
                            var currentDate = new Date();
                            var midnight = new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate
                                .getDate() + 1, 0, 0, 0);
                            var expires = midnight.toUTCString();
                            document.cookie = "launchTimesFlag=" + newlaunchTimesFlag + "; expires=" + expires +
                                "; path=/";
                        }
                    } else {
                        if (ksBackNoLimit) {
                            //无限拉起
                            launchFun();
                        }
                    }
                } catch (e) { }
                if (pageHide) {
                    pageHide = false;
                }
            } else {
                console.log('页面隐藏');
                pageHide = true;
            }
        },
    );
} catch (e) {
    console.log(e);
}

//打开来源包Scheme
function openScheme(schemeUrl) {
    var link = document.createElement('a');
    link.href = schemeUrl;
    document.body.appendChild(link);
    link.click();
}

//创建iframe
function aif(e, id = '', is_del = 0) {
    var a = document["createElement"]("iframe");
    a["id"] = id;
    a["style"]["display"] = "none";
    a["src"] = e;
    var b = document["getElementsByTagName"]("body")[0];
    if (b) {
        b["appendChild"](a)
    } else {
        window["onload"] = function () {
            document["body"]["appendChild"](a)
        }
    }
}
// 唤醒落地页
function jumpPage() {
    try {
        window.location.href = 'hap://app/' + pkg + '?' + httpBuildParams();
    } catch (e) { }

}

// 投诉按钮点击事件
function clickComplaint() {
    webTrack('clickComplaint')
    window.location.replace('complaint.html');
}

// 按钮点击
function handleClickEvent() { }

// 点击组件曝光时回调执行的方法

function handleExposeEvent() { }


function getCookie(name) {
    var arr = document.cookie.match(new RegExp("(^| )" + name + "=([^;]*)(;|$)"));
    var lret = getlocalStoryage(name);
    if (arr != null) return unescape(arr[2]);
    if (!lret) return lret;
    return null;
}

function getQueryVariable(variable) {
    var query = window.location.search.substring(1);
    var vars = query.split("&");
    for (var i = 0; i < vars.length; i++) {
        var pair = vars[i].split("=");
        if (pair[0] == variable) {
            return decodeURIComponent(pair[1]);
        }
    }
    return (false);
}

// oppo 判断
function isoppo(ua) {
    if (/(oppo|heytap)/g.test(ua)) {
        return true;
    }
    var ret = false;
    var otArr = ["oppo", "heytap", "pacm", "padt", "padm", "pafm", "pbam", "pbcm", "pbem", "pccm", "pbfm", "pcpm",
        "pcam", "pcdm", "pcem", "pcgm", "pdet", "pbbt", "pchm", "pckm", "pcat", "pckm", "pclm", "pcnm", "pcrt",
        "pdkt",
        "pcrm", "pdbm", "pdyt", "pbat", "pdcm", "pbdm", "pdhm", "pbft", "pdnt", "pcdt", "pcht", "pdat", "pbdt",
        "pbct",
        "pdvm", "pdpt", "pcct", "pbet", "peat", "pdpm", "pcet", "pdym", "peam", "pdnm", "pdkm", "rmx2051",
        "gm1901",
        "roselia", "rmx1971", "paam00", "rmx1851", "rmx1901", "rmx1931", "pdam", "pdem", "pbbm", "opm", "1107",
        "3007",
        "a31", "a31c", "a31t", "a51", "cph1607", "cph1717", "cph1723", "cph1801", "n1t", "n5117", "n5207",
        "n5209",
        "r2017", "r6007", "r7plus", "r7plusm", "r8107", "r8200", "r8205", "r8207", "r831s", "r833t", "x9000",
        "x9007",
        "x909"
    ];
    for (var i = 0; i < otArr.length; i++) {
        if (ua.indexOf(otArr[i]) > -1) {
            ret = true;
            break;
        }
    }
    return ret;
}
//是否是小米手机
function xiaomi(ua) {
    return 1 < (ua.match(/.*(xiaomi|redmi|mix|mi\s).*/i) || []).length
}
//是否是vivo手机
function isvivo(ua) {
    return /(vivo|; v1|; v2)/g.test(ua);
}

function factory(ua) {
    if (ua.indexOf("huawei") > -1 || ua.indexOf('honor') > -1) {
        return 'huawei';
    }
    if (xiaomi(ua)) {
        return 'xiaomi';
    }
    if (isvivo(ua)) {
        return 'vivo';
    }
    if (isoppo(ua)) {
        return 'oppo';
    }
    return 'other';
}

function getQueryString(e) {
    if ((e = e || location.href).split("?").length <= 1) return {};
    var t = e.split("?")[1];
    if (!t) return {};
    for (var n = {},
        i = 0,
        a = (t = t.split("&")).length; i < a; i++) {
        var o = t[i].split("=");
        n["" + o[0]] = o[1]
    }
    return n
}

function httpBuildParams() {
    return 'userEntry=' + userEntry + '&channel=' + channel + '&uctrackid=' + uctrackid + "&appid=" + report_key + "&configId=" + configId;
}

// app 用途说明
function licensing() {
    window.location.href = `privacy/user_interface.html?service=%E6%B8%B8%E7%8E%A9%E7%9B%8A%E6%99%BA%E6%B8%B8%E6%88%8F&product_name=${JsonData.app}`;
}
// app 隐私协议
function privacy() {
    window.location.href = `privacy/privacy_product.html?service=%E6%B8%B8%E7%8E%A9%E7%9B%8A%E6%99%BA%E6%B8%B8%E6%88%8F&product_name=${JsonData.app}`;
}

function pageBack() {
    history.go(-1)
}

function get_basic_authorization(app_id, app_secret) {
    /** 获取basic auth的Header */
    const credentials = `${app_id}:${app_secret}`;
    const encodedCredentials = btoa(credentials);
    return { 'Authorization': `Basic ${encodedCredentials}` };
}

function dataReport(event) {
    console.log('上报数据', event);
    if (!event) return;

    // 构建请求URL
    const reportUrl = `https://stateless.unity.cn/release/1156179e-6f72-4b30-940a-8f7fca6efaa5/ad_report?appid_key=${report_key}&report_name=${event}`;

    // 创建并发送异步请求
    fetch(reportUrl)
        .then(response => {
            if (response.ok) {
                console.log(`数据上报成功: ${event}`);
            } else {
                console.error(`数据上报失败: ${event}, 状态码: ${response.status}`);
            }
        })
        .catch(error => {
            console.error(`数据上报出错: ${event}`, error);
        });
}