# ldy.html 性能优化总结

## 优化目标
实现"空白页 → 加载外部脚本 → 执行调起逻辑 → 动态加载页面内容"的优化加载流程，确保关键的调起功能能够最快速度执行。

## 主要优化措施

### 1. 初始状态优化
**优化前：**
- 页面初始加载时包含完整的DOM结构和内容
- 同时加载CSS和JavaScript文件
- 调起逻辑依赖于完整页面加载

**优化后：**
- 页面初始状态为完全空白，只包含一个隐藏的容器元素
- 移除所有初始可见的DOM元素、样式和内容
- 实现真正的"空白页"初始状态

### 2. 外部脚本优先加载
**优化策略：**
- 页面DOM加载完成后立即优先加载外部脚本 `qa_router.min.js`
- 使用 `script.onload` 和 `script.onerror` 确保无论加载成功或失败都能继续执行
- 外部脚本加载完成后立即执行调起逻辑

### 3. 调起逻辑立即执行
**关键改进：**
- 将核心调起函数 `launchFun()` 内联到HTML中
- 外部脚本加载完成后立即调用调起逻辑
- 不等待页面内容渲染，优先执行应用调起功能
- 添加容错机制，即使外部脚本加载失败也能执行基础调起逻辑

### 4. 代码整合优化
**整合内容：**
- 将关键调起相关的逻辑代码直接整合到 `ldy.html` 文件中
- 内联了以下关键函数：
  - `getQueryVariable()` - URL参数解析
  - `launchFun()` - 核心调起函数
  - `clickHap()` - 点击跳转函数
  - `jumpPage()` - 页面唤醒函数
  - `dataReport()` - 数据上报函数
  - 设备厂商判断函数（`isoppo`, `xiaomi`, `isvivo`, `factory`）

### 5. 动态内容加载
**延迟加载策略：**
- 调起逻辑执行100ms后再开始加载页面内容
- 异步加载CSS文件（`./css/style.css`）
- 异步加载完整的main.js功能
- 动态渲染页面DOM结构
- 异步加载配置数据并更新页面内容

### 6. 内联关键样式
**样式优化：**
- 将首屏必需的CSS样式内联到HTML中
- 确保页面在外部CSS加载前也能正常显示
- 包含基础布局、动画和关键组件样式
- 完整CSS文件异步加载，不阻塞渲染

### 7. 异步配置加载
**API请求优化：**
- 将Unity API配置请求从同步改为异步
- 使用 `fetch()` 替代同步的 `XMLHttpRequest`
- 添加错误处理，配置加载失败时使用默认配置
- 不阻塞调起逻辑的执行

### 8. 减少重复调用
**代码优化：**
- 移除原main.js中重复的 `launchFun()` 调用（原来调用了3次）
- 优化事件绑定，避免重复绑定
- 简化页面初始化流程

## 性能提升效果

### 加载时间优化
- **关键路径缩短**：调起逻辑不再依赖完整页面加载
- **并行加载**：外部脚本和页面内容可以并行处理
- **减少阻塞**：CSS和非关键JS异步加载，不阻塞调起逻辑

### 用户体验提升
- **更快调起**：用户点击后能更快速地跳转到目标应用
- **渐进式加载**：页面内容逐步显示，不影响核心功能
- **容错性强**：即使部分资源加载失败，核心调起功能仍可正常工作

### 网络请求优化
- **减少依赖**：关键逻辑内联，减少网络请求数量
- **异步处理**：非关键资源异步加载，提升整体性能
- **智能降级**：配置加载失败时自动使用默认配置

## 技术实现细节

### 加载流程
1. **DOM Ready** → 立即加载外部脚本
2. **脚本加载完成** → 执行调起逻辑 + 数据上报
3. **100ms延迟** → 开始加载页面内容
4. **并行加载** → CSS + main.js + 页面渲染
5. **异步配置** → 加载Unity配置并更新页面

### 容错机制
- 外部脚本加载失败时继续执行调起逻辑
- 配置API请求失败时使用默认配置
- DOM元素不存在时跳过相关操作
- 所有关键操作都包含try-catch保护

### 兼容性保证
- 保持原有的所有功能和接口
- 兼容现有的URL参数和配置
- 保持与main.js的功能一致性
- 支持所有原有的设备厂商判断逻辑

## 总结
通过这次优化，成功实现了"空白页 → 加载外部脚本 → 执行调起逻辑 → 动态加载页面内容"的性能优化策略。关键的应用调起功能现在能够以最快的速度执行，同时保持了完整的页面功能和用户体验。优化后的页面在保证功能完整性的同时，显著提升了加载性能和用户体验。
